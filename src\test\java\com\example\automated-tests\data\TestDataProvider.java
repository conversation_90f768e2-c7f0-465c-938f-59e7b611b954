package com.example.automated-tests.data;

import org.testng.annotations.DataProvider;

/**
 * Test data provider class containing test data for various test scenarios.
 * Centralizes test data management for better maintainability.
 * 
 * <AUTHOR> Automation Team
 * @version 1.0
 */
public class TestDataProvider {
    
    /**
     * Provides valid login credentials for successful login tests.
     * 
     * @return Object array containing valid username and password combinations
     */
    @DataProvider(name = "validLoginData")
    public static Object[][] getValidLoginData() {
        return new Object[][] {
            {"validuser1", "validpass1"},
            {"<EMAIL>", "TestPass123!"},
            {"admin", "admin123"}
        };
    }
    
    /**
     * Provides invalid login credentials for failed login tests.
     * 
     * @return Object array containing invalid username and password combinations
     */
    @DataProvider(name = "invalidLoginData")
    public static Object[][] getInvalidLoginData() {
        return new Object[][] {
            {"invaliduser", "invalidpass", "Invalid username and password"},
            {"validuser1", "wrongpass", "Valid username, invalid password"},
            {"wronguser", "validpass1", "Invalid username, valid password"},
            {"", "validpass1", "Empty username"},
            {"validuser1", "", "Empty password"},
            {"", "", "Empty username and password"}
        };
    }
    
    /**
     * Provides SQL injection test data to verify input sanitization.
     * 
     * @return Object array containing SQL injection attempts
     */
    @DataProvider(name = "sqlInjectionData")
    public static Object[][] getSqlInjectionData() {
        return new Object[][] {
            {"admin'--", "password"},
            {"admin' OR '1'='1", "password"},
            {"admin'; DROP TABLE users;--", "password"},
            {"admin", "password' OR '1'='1"},
            {"' OR 1=1--", "' OR 1=1--"},
            {"admin\"; DROP TABLE users;--", "password"}
        };
    }
    
    /**
     * Provides boundary test data for input field validation.
     * 
     * @return Object array containing boundary test cases
     */
    @DataProvider(name = "boundaryTestData")
    public static Object[][] getBoundaryTestData() {
        // Generate long strings for boundary testing
        String longUsername = "a".repeat(256); // 256 characters
        String longPassword = "p".repeat(256); // 256 characters
        
        return new Object[][] {
            {longUsername, "password", "Username too long"},
            {"username", longPassword, "Password too long"},
            {"a", "p", "Minimum length username and password"},
            {"<EMAIL>", "Pass123!", "Valid email format username"}
        };
    }
    
    /**
     * Provides special character test data.
     * 
     * @return Object array containing special character test cases
     */
    @DataProvider(name = "specialCharacterData")
    public static Object[][] getSpecialCharacterData() {
        return new Object[][] {
            {"<EMAIL>", "Pass123!", "Email with special chars"},
            {"user.name", "password", "Username with dot"},
            {"user_name", "password", "Username with underscore"},
            {"username", "P@ssw0rd!", "Password with special chars"},
            {"user name", "password", "Username with space"},
            {"username", "pass word", "Password with space"}
        };
    }
    
    /**
     * Provides test data for remember me functionality testing.
     * 
     * @return Object array containing remember me test scenarios
     */
    @DataProvider(name = "rememberMeData")
    public static Object[][] getRememberMeData() {
        return new Object[][] {
            {"validuser1", "validpass1", true, "Remember me checked"},
            {"validuser1", "validpass1", false, "Remember me unchecked"}
        };
    }
    
    // Constants for commonly used test data
    public static final class LoginCredentials {
        public static final String VALID_USERNAME = "testuser";
        public static final String VALID_PASSWORD = "testpass123";
        public static final String INVALID_USERNAME = "invaliduser";
        public static final String INVALID_PASSWORD = "invalidpass";
        public static final String EMPTY_STRING = "";
    }
    
    // Error messages expected in the application
    public static final class ExpectedMessages {
        public static final String INVALID_CREDENTIALS = "Invalid username or password";
        public static final String EMPTY_USERNAME = "Username is required";
        public static final String EMPTY_PASSWORD = "Password is required";
        public static final String LOGIN_SUCCESS = "Login successful";
        public static final String ACCOUNT_LOCKED = "Account is locked";
    }
    
    // Page URLs and titles
    public static final class PageConstants {
        public static final String LOGIN_PAGE_URL = "/login";
        public static final String DASHBOARD_PAGE_URL = "/dashboard";
        public static final String LOGIN_PAGE_TITLE = "Login";
        public static final String DASHBOARD_PAGE_TITLE = "Dashboard";
    }
}
