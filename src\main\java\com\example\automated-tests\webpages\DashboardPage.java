package com.example.automated-tests.webpages;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

public class DashboardPage extends BasePage {

    private WebElement navigationHeader;

    public void openDashboard() {
        driver.get("http://your-website-url/dashboard");
    }

    public boolean isNavigationHeaderPresent() {
        this.navigationHeader = driver.findElement(By.tagName("header"));
        return navigationHeader.isDisplayed();
    }
}