#!/bin/bash

echo "========================================"
echo "   Login Page Test Automation Runner"
echo "========================================"
echo

# Check if <PERSON><PERSON> is installed
if ! command -v mvn &> /dev/null; then
    echo "ERROR: <PERSON><PERSON> is not installed or not in PATH"
    echo "Please install <PERSON><PERSON> and try again"
    exit 1
fi

echo "Maven found. Starting test execution..."
echo

# Set default values
BROWSER="chrome"
BASE_URL="http://your-website-url"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -browser)
            BROWSER="$2"
            shift 2
            ;;
        -url)
            BASE_URL="$2"
            shift 2
            ;;
        -help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

show_help() {
    echo
    echo "Usage: ./run-tests.sh [options]"
    echo
    echo "Options:"
    echo "  -browser <browser>    Browser to use (chrome, firefox, edge)"
    echo "  -url <url>           Base URL of the application"
    echo "  -help                Show this help message"
    echo
    echo "Examples:"
    echo "  ./run-tests.sh"
    echo "  ./run-tests.sh -browser firefox"
    echo "  ./run-tests.sh -url http://localhost:8080"
    echo "  ./run-tests.sh -browser chrome -url http://test.example.com"
    echo
}

echo "Running tests with:"
echo "Browser: $BROWSER"
echo "Base URL: $BASE_URL"
echo

# Clean and compile
echo "Step 1: Cleaning and compiling project..."
mvn clean compile test-compile
if [ $? -ne 0 ]; then
    echo "ERROR: Compilation failed"
    exit 1
fi

echo
echo "Step 2: Running tests..."
mvn test -Dbrowser="$BROWSER" -DbaseUrl="$BASE_URL"

echo
echo "Step 3: Generating Allure report..."
mvn allure:report

echo
echo "========================================"
echo "Test execution completed!"
echo "========================================"
echo
echo "Reports available at:"
echo "- TestNG Report: target/surefire-reports/index.html"
echo "- Allure Report: target/site/allure-maven-plugin/index.html"
echo "- Screenshots: test-output/screenshots/"
echo

# Ask if user wants to open reports
read -p "Open Allure report in browser? (y/n): " OPEN_REPORT
if [[ "$OPEN_REPORT" =~ ^[Yy]$ ]]; then
    mvn allure:serve
fi

exit 0
