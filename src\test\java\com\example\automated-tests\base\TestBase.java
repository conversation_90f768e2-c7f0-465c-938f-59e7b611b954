package com.example.automated-tests.base;

import io.github.bonigarcia.wdm.WebDriverManager;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.edge.EdgeDriver;
import org.openqa.selenium.edge.EdgeOptions;
import org.openqa.selenium.firefox.FirefoxDriver;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.annotations.*;

import java.time.Duration;

/**
 * Base test class providing WebDriver setup and teardown functionality.
 * This class manages WebDriver lifecycle and provides common test utilities.
 * 
 * <AUTHOR> Automation Team
 * @version 1.0
 */
public class TestBase {
    
    private static final Logger logger = LoggerFactory.getLogger(TestBase.class);
    
    protected WebDriver driver;
    protected WebDriverWait wait;
    
    // Test configuration parameters
    private String browser = "chrome";
    private String baseUrl = "http://your-website-url";
    private final int IMPLICIT_WAIT_TIMEOUT = 10;
    private final int EXPLICIT_WAIT_TIMEOUT = 15;
    
    /**
     * Sets up the test environment before each test method.
     * Initializes WebDriver based on browser parameter and configures timeouts.
     * 
     * @param browser Browser type from TestNG parameter
     * @param baseUrl Base URL from TestNG parameter
     */
    @BeforeMethod(alwaysRun = true)
    @Parameters({"browser", "baseUrl"})
    public void setUp(@Optional("chrome") String browser, @Optional("http://your-website-url") String baseUrl) {
        this.browser = browser.toLowerCase();
        this.baseUrl = baseUrl;
        
        logger.info("Setting up test environment with browser: {} and baseUrl: {}", browser, baseUrl);
        
        initializeWebDriver();
        configureWebDriver();
        
        logger.info("Test setup completed successfully");
    }
    
    /**
     * Initializes WebDriver based on the specified browser type.
     * Uses WebDriverManager for automatic driver management.
     */
    private void initializeWebDriver() {
        try {
            switch (browser) {
                case "chrome":
                    WebDriverManager.chromedriver().setup();
                    ChromeOptions chromeOptions = new ChromeOptions();
                    chromeOptions.addArguments("--disable-blink-features=AutomationControlled");
                    chromeOptions.addArguments("--disable-extensions");
                    chromeOptions.addArguments("--no-sandbox");
                    chromeOptions.addArguments("--disable-dev-shm-usage");
                    // Uncomment for headless mode
                    // chromeOptions.addArguments("--headless");
                    driver = new ChromeDriver(chromeOptions);
                    break;
                    
                case "firefox":
                    WebDriverManager.firefoxdriver().setup();
                    FirefoxOptions firefoxOptions = new FirefoxOptions();
                    // Uncomment for headless mode
                    // firefoxOptions.addArguments("--headless");
                    driver = new FirefoxDriver(firefoxOptions);
                    break;
                    
                case "edge":
                    WebDriverManager.edgedriver().setup();
                    EdgeOptions edgeOptions = new EdgeOptions();
                    edgeOptions.addArguments("--disable-blink-features=AutomationControlled");
                    driver = new EdgeDriver(edgeOptions);
                    break;
                    
                default:
                    throw new IllegalArgumentException("Browser not supported: " + browser);
            }
            
            logger.info("WebDriver initialized successfully for browser: {}", browser);
            
        } catch (Exception e) {
            logger.error("Failed to initialize WebDriver for browser: {}", browser, e);
            throw new RuntimeException("WebDriver initialization failed", e);
        }
    }
    
    /**
     * Configures WebDriver with timeouts and window settings.
     */
    private void configureWebDriver() {
        // Set timeouts
        driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(IMPLICIT_WAIT_TIMEOUT));
        driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(30));
        driver.manage().timeouts().scriptTimeout(Duration.ofSeconds(30));
        
        // Maximize window
        driver.manage().window().maximize();
        
        // Initialize WebDriverWait
        wait = new WebDriverWait(driver, Duration.ofSeconds(EXPLICIT_WAIT_TIMEOUT));
        
        logger.info("WebDriver configured with timeouts and window settings");
    }
    
    /**
     * Cleans up the test environment after each test method.
     * Quits the WebDriver instance and performs cleanup.
     */
    @AfterMethod(alwaysRun = true)
    public void tearDown() {
        if (driver != null) {
            try {
                logger.info("Cleaning up test environment");
                driver.quit();
                logger.info("WebDriver quit successfully");
            } catch (Exception e) {
                logger.error("Error during WebDriver cleanup", e);
            }
        }
    }
    
    /**
     * Gets the current WebDriver instance.
     * 
     * @return WebDriver instance
     */
    public WebDriver getDriver() {
        return driver;
    }
    
    /**
     * Gets the WebDriverWait instance.
     * 
     * @return WebDriverWait instance
     */
    public WebDriverWait getWait() {
        return wait;
    }
    
    /**
     * Gets the base URL for the application.
     * 
     * @return Base URL string
     */
    public String getBaseUrl() {
        return baseUrl;
    }
}
