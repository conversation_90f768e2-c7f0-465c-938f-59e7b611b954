package com.example.automated-tests.utils;

import io.qameta.allure.Attachment;
import org.apache.commons.io.FileUtils;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Utility class providing common test helper methods.
 * Contains methods for screenshots, waits, and other test utilities.
 * 
 * <AUTHOR> Automation Team
 * @version 1.0
 */
public class TestUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(TestUtils.class);
    private static final String SCREENSHOT_DIR = "test-output/screenshots/";
    
    /**
     * Takes a screenshot and saves it to the specified directory.
     * 
     * @param driver WebDriver instance
     * @param testName Name of the test for screenshot naming
     * @return Path to the saved screenshot
     */
    public static String takeScreenshot(WebDriver driver, String testName) {
        try {
            // Create screenshots directory if it doesn't exist
            File screenshotDir = new File(SCREENSHOT_DIR);
            if (!screenshotDir.exists()) {
                screenshotDir.mkdirs();
            }
            
            // Generate timestamp for unique filename
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
            String fileName = testName + "_" + timestamp + ".png";
            String filePath = SCREENSHOT_DIR + fileName;
            
            // Take screenshot
            TakesScreenshot takesScreenshot = (TakesScreenshot) driver;
            File sourceFile = takesScreenshot.getScreenshotAs(OutputType.FILE);
            File destFile = new File(filePath);
            
            FileUtils.copyFile(sourceFile, destFile);
            
            logger.info("Screenshot saved: {}", filePath);
            return filePath;
            
        } catch (IOException e) {
            logger.error("Failed to take screenshot for test: {}", testName, e);
            return null;
        }
    }
    
    /**
     * Takes a screenshot and attaches it to Allure report.
     * 
     * @param driver WebDriver instance
     * @return Screenshot as byte array for Allure attachment
     */
    @Attachment(value = "Screenshot", type = "image/png")
    public static byte[] takeScreenshotForAllure(WebDriver driver) {
        try {
            return ((TakesScreenshot) driver).getScreenshotAs(OutputType.BYTES);
        } catch (Exception e) {
            logger.error("Failed to take screenshot for Allure report", e);
            return new byte[0];
        }
    }
    
    /**
     * Waits for an element to be visible and returns it.
     * 
     * @param wait WebDriverWait instance
     * @param element WebElement to wait for
     * @return WebElement once it's visible
     */
    public static WebElement waitForElementToBeVisible(WebDriverWait wait, WebElement element) {
        try {
            return wait.until(ExpectedConditions.visibilityOf(element));
        } catch (Exception e) {
            logger.error("Element not visible within timeout", e);
            throw e;
        }
    }
    
    /**
     * Waits for an element to be clickable and returns it.
     * 
     * @param wait WebDriverWait instance
     * @param element WebElement to wait for
     * @return WebElement once it's clickable
     */
    public static WebElement waitForElementToBeClickable(WebDriverWait wait, WebElement element) {
        try {
            return wait.until(ExpectedConditions.elementToBeClickable(element));
        } catch (Exception e) {
            logger.error("Element not clickable within timeout", e);
            throw e;
        }
    }
    
    /**
     * Clears and enters text into an input field.
     * 
     * @param element WebElement input field
     * @param text Text to enter
     */
    public static void clearAndEnterText(WebElement element, String text) {
        try {
            element.clear();
            element.sendKeys(text);
            logger.debug("Entered text '{}' into element", text);
        } catch (Exception e) {
            logger.error("Failed to enter text '{}' into element", text, e);
            throw e;
        }
    }
    
    /**
     * Safely clicks an element with error handling.
     * 
     * @param element WebElement to click
     */
    public static void safeClick(WebElement element) {
        try {
            element.click();
            logger.debug("Successfully clicked element");
        } catch (Exception e) {
            logger.error("Failed to click element", e);
            throw e;
        }
    }
    
    /**
     * Checks if an element is displayed.
     * 
     * @param element WebElement to check
     * @return true if element is displayed, false otherwise
     */
    public static boolean isElementDisplayed(WebElement element) {
        try {
            return element.isDisplayed();
        } catch (Exception e) {
            logger.debug("Element not displayed or not found");
            return false;
        }
    }
    
    /**
     * Gets text from an element safely.
     * 
     * @param element WebElement to get text from
     * @return Element text or empty string if not found
     */
    public static String getElementText(WebElement element) {
        try {
            return element.getText().trim();
        } catch (Exception e) {
            logger.error("Failed to get text from element", e);
            return "";
        }
    }
}
