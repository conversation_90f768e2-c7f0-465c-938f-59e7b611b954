@echo off
echo ========================================
echo    Login Page Test Automation Runner
echo ========================================
echo.

REM Check if <PERSON><PERSON> is installed
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: <PERSON><PERSON> is not installed or not in PATH
    echo Please install <PERSON><PERSON> and try again
    pause
    exit /b 1
)

echo Maven found. Starting test execution...
echo.

REM Set default values
set BROWSER=chrome
set BASE_URL=http://your-website-url

REM Parse command line arguments
:parse
if "%~1"=="" goto :run
if "%~1"=="-browser" (
    set BROWSER=%~2
    shift
    shift
    goto :parse
)
if "%~1"=="-url" (
    set BASE_URL=%~2
    shift
    shift
    goto :parse
)
if "%~1"=="-help" goto :help
shift
goto :parse

:run
echo Running tests with:
echo Browser: %BROWSER%
echo Base URL: %BASE_URL%
echo.

REM Clean and compile
echo Step 1: Cleaning and compiling project...
call mvn clean compile test-compile
if %errorlevel% neq 0 (
    echo ERROR: Compilation failed
    pause
    exit /b 1
)

echo.
echo Step 2: Running tests...
call mvn test -Dbrowser=%BROWSER% -DbaseUrl=%BASE_URL%

echo.
echo Step 3: Generating Allure report...
call mvn allure:report

echo.
echo ========================================
echo Test execution completed!
echo ========================================
echo.
echo Reports available at:
echo - TestNG Report: target\surefire-reports\index.html
echo - Allure Report: target\site\allure-maven-plugin\index.html
echo - Screenshots: test-output\screenshots\
echo.

REM Ask if user wants to open reports
set /p OPEN_REPORT="Open Allure report in browser? (y/n): "
if /i "%OPEN_REPORT%"=="y" (
    call mvn allure:serve
)

pause
exit /b 0

:help
echo.
echo Usage: run-tests.bat [options]
echo.
echo Options:
echo   -browser ^<browser^>    Browser to use (chrome, firefox, edge)
echo   -url ^<url^>           Base URL of the application
echo   -help                Show this help message
echo.
echo Examples:
echo   run-tests.bat
echo   run-tests.bat -browser firefox
echo   run-tests.bat -url http://localhost:8080
echo   run-tests.bat -browser chrome -url http://test.example.com
echo.
pause
exit /b 0
