package com.example.automated-tests.webpages;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.FindBy;
import org.openqa.selenium.support.PageFactory;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Page Object Model class for Login Page functionality.
 * Implements Page Factory pattern with WebElement annotations.
 * Provides methods for login operations and validations.
 *
 * <AUTHOR> Automation Team
 * @version 2.0
 */
public class LoginPage {

    private static final Logger logger = LoggerFactory.getLogger(LoginPage.class);

    private final WebDriver driver;
    private final WebDriverWait wait;

    // Page URL and title constants
    private static final String LOGIN_PAGE_URL = "/login";
    private static final String LOGIN_PAGE_TITLE = "Login";

    // Page elements using Page Factory pattern
    @FindBy(id = "username")
    private WebElement usernameField;

    @FindBy(id = "password")
    private WebElement passwordField;

    @FindBy(id = "loginButton")
    private WebElement loginButton;

    @FindBy(id = "rememberMe")
    private WebElement rememberMeCheckbox;

    @FindBy(linkText = "Forgot Password?")
    private WebElement forgotPasswordLink;

    @FindBy(className = "error-message")
    private WebElement errorMessage;

    @FindBy(className = "success-message")
    private WebElement successMessage;

    @FindBy(xpath = "//div[@class='login-form']")
    private WebElement loginForm;

    @FindBy(xpath = "//h1[contains(text(), 'Login')]")
    private WebElement loginPageHeader;

    /**
     * Constructor to initialize LoginPage with WebDriver and WebDriverWait.
     *
     * @param driver WebDriver instance
     * @param wait WebDriverWait instance
     */
    public LoginPage(WebDriver driver, WebDriverWait wait) {
        this.driver = driver;
        this.wait = wait;
        PageFactory.initElements(driver, this);
        logger.info("LoginPage initialized successfully");
    }

    /**
     * Opens the login page by navigating to the login URL.
     *
     * @param baseUrl Base URL of the application
     */
    public void openLoginPage(String baseUrl) {
        String fullUrl = baseUrl + LOGIN_PAGE_URL;
        logger.info("Navigating to login page: {}", fullUrl);
        driver.get(fullUrl);
        waitForPageToLoad();
    }

    /**
     * Waits for the login page to load completely.
     */
    public void waitForPageToLoad() {
        try {
            wait.until(ExpectedConditions.visibilityOf(loginForm));
            wait.until(ExpectedConditions.elementToBeClickable(loginButton));
            logger.info("Login page loaded successfully");
        } catch (Exception e) {
            logger.error("Login page failed to load within timeout", e);
            throw new RuntimeException("Login page load timeout", e);
        }
    }

    /**
     * Enters username into the username field.
     * Clears the field first, then enters the provided username.
     *
     * @param username Username to enter
     */
    public void enterUsername(String username) {
        try {
            wait.until(ExpectedConditions.visibilityOf(usernameField));
            usernameField.clear();
            usernameField.sendKeys(username);
            logger.info("Username entered successfully");
        } catch (Exception e) {
            logger.error("Failed to enter username: {}", username, e);
            throw new RuntimeException("Failed to enter username", e);
        }
    }

    /**
     * Enters password into the password field.
     * Clears the field first, then enters the provided password.
     *
     * @param password Password to enter
     */
    public void enterPassword(String password) {
        try {
            wait.until(ExpectedConditions.visibilityOf(passwordField));
            passwordField.clear();
            passwordField.sendKeys(password);
            logger.info("Password entered successfully");
        } catch (Exception e) {
            logger.error("Failed to enter password", e);
            throw new RuntimeException("Failed to enter password", e);
        }
    }

    /**
     * Clicks the login button to submit the login form.
     */
    public void clickLoginButton() {
        try {
            wait.until(ExpectedConditions.elementToBeClickable(loginButton));
            loginButton.click();
            logger.info("Login button clicked successfully");
        } catch (Exception e) {
            logger.error("Failed to click login button", e);
            throw new RuntimeException("Failed to click login button", e);
        }
    }

    /**
     * Performs complete login operation with username and password.
     *
     * @param username Username for login
     * @param password Password for login
     */
    public void performLogin(String username, String password) {
        logger.info("Performing login with username: {}", username);
        enterUsername(username);
        enterPassword(password);
        clickLoginButton();
    }

    /**
     * Performs login with remember me option.
     *
     * @param username Username for login
     * @param password Password for login
     * @param rememberMe Whether to check remember me checkbox
     */
    public void performLoginWithRememberMe(String username, String password, boolean rememberMe) {
        logger.info("Performing login with remember me: {}", rememberMe);
        enterUsername(username);
        enterPassword(password);

        if (rememberMe && isRememberMeCheckboxPresent()) {
            setRememberMeCheckbox(true);
        }

        clickLoginButton();
    }

    /**
     * Sets the state of the remember me checkbox.
     *
     * @param check True to check the checkbox, false to uncheck
     */
    public void setRememberMeCheckbox(boolean check) {
        try {
            if (isRememberMeCheckboxPresent()) {
                boolean isChecked = rememberMeCheckbox.isSelected();
                if (check && !isChecked) {
                    rememberMeCheckbox.click();
                    logger.info("Remember me checkbox checked");
                } else if (!check && isChecked) {
                    rememberMeCheckbox.click();
                    logger.info("Remember me checkbox unchecked");
                }
            }
        } catch (Exception e) {
            logger.error("Failed to set remember me checkbox", e);
            throw new RuntimeException("Failed to set remember me checkbox", e);
        }
    }

    /**
     * Clicks the forgot password link.
     */
    public void clickForgotPasswordLink() {
        try {
            wait.until(ExpectedConditions.elementToBeClickable(forgotPasswordLink));
            forgotPasswordLink.click();
            logger.info("Forgot password link clicked successfully");
        } catch (Exception e) {
            logger.error("Failed to click forgot password link", e);
            throw new RuntimeException("Failed to click forgot password link", e);
        }
    }

    // Validation methods

    /**
     * Checks if the login page is displayed correctly.
     *
     * @return True if login page is displayed, false otherwise
     */
    public boolean isLoginPageDisplayed() {
        try {
            return loginForm.isDisplayed() &&
                   usernameField.isDisplayed() &&
                   passwordField.isDisplayed() &&
                   loginButton.isDisplayed();
        } catch (Exception e) {
            logger.debug("Login page elements not displayed", e);
            return false;
        }
    }

    /**
     * Checks if the login page title is correct.
     *
     * @return True if title matches expected title, false otherwise
     */
    public boolean isLoginPageTitleCorrect() {
        try {
            return driver.getTitle().contains(LOGIN_PAGE_TITLE);
        } catch (Exception e) {
            logger.error("Failed to get page title", e);
            return false;
        }
    }

    /**
     * Checks if error message is displayed.
     *
     * @return True if error message is visible, false otherwise
     */
    public boolean isErrorMessageDisplayed() {
        try {
            return errorMessage.isDisplayed();
        } catch (Exception e) {
            logger.debug("Error message not displayed", e);
            return false;
        }
    }

    /**
     * Gets the error message text.
     *
     * @return Error message text or empty string if not found
     */
    public String getErrorMessageText() {
        try {
            if (isErrorMessageDisplayed()) {
                return errorMessage.getText().trim();
            }
            return "";
        } catch (Exception e) {
            logger.error("Failed to get error message text", e);
            return "";
        }
    }

    /**
     * Checks if success message is displayed.
     *
     * @return True if success message is visible, false otherwise
     */
    public boolean isSuccessMessageDisplayed() {
        try {
            return successMessage.isDisplayed();
        } catch (Exception e) {
            logger.debug("Success message not displayed", e);
            return false;
        }
    }

    /**
     * Gets the success message text.
     *
     * @return Success message text or empty string if not found
     */
    public String getSuccessMessageText() {
        try {
            if (isSuccessMessageDisplayed()) {
                return successMessage.getText().trim();
            }
            return "";
        } catch (Exception e) {
            logger.error("Failed to get success message text", e);
            return "";
        }
    }

    /**
     * Checks if remember me checkbox is present on the page.
     *
     * @return True if checkbox is present, false otherwise
     */
    public boolean isRememberMeCheckboxPresent() {
        try {
            return rememberMeCheckbox.isDisplayed();
        } catch (Exception e) {
            logger.debug("Remember me checkbox not present", e);
            return false;
        }
    }

    /**
     * Checks if forgot password link is present on the page.
     *
     * @return True if link is present, false otherwise
     */
    public boolean isForgotPasswordLinkPresent() {
        try {
            return forgotPasswordLink.isDisplayed();
        } catch (Exception e) {
            logger.debug("Forgot password link not present", e);
            return false;
        }
    }

    /**
     * Gets the current username field value.
     *
     * @return Username field value
     */
    public String getUsernameFieldValue() {
        try {
            return usernameField.getAttribute("value");
        } catch (Exception e) {
            logger.error("Failed to get username field value", e);
            return "";
        }
    }

    /**
     * Gets the current password field value.
     *
     * @return Password field value
     */
    public String getPasswordFieldValue() {
        try {
            return passwordField.getAttribute("value");
        } catch (Exception e) {
            logger.error("Failed to get password field value", e);
            return "";
        }
    }

    /**
     * Checks if username field is enabled.
     *
     * @return True if enabled, false otherwise
     */
    public boolean isUsernameFieldEnabled() {
        try {
            return usernameField.isEnabled();
        } catch (Exception e) {
            logger.error("Failed to check username field status", e);
            return false;
        }
    }

    /**
     * Checks if password field is enabled.
     *
     * @return True if enabled, false otherwise
     */
    public boolean isPasswordFieldEnabled() {
        try {
            return passwordField.isEnabled();
        } catch (Exception e) {
            logger.error("Failed to check password field status", e);
            return false;
        }
    }

    /**
     * Checks if login button is enabled.
     *
     * @return True if enabled, false otherwise
     */
    public boolean isLoginButtonEnabled() {
        try {
            return loginButton.isEnabled();
        } catch (Exception e) {
            logger.error("Failed to check login button status", e);
            return false;
        }
    }

    /**
     * Clears both username and password fields.
     */
    public void clearLoginFields() {
        try {
            usernameField.clear();
            passwordField.clear();
            logger.info("Login fields cleared successfully");
        } catch (Exception e) {
            logger.error("Failed to clear login fields", e);
            throw new RuntimeException("Failed to clear login fields", e);
        }
    }
}