package com.example.automated-tests.webpages;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;

public class LoginPage extends BasePage {

    private final String usernameFieldId = "username";
    private final String passwordFieldId = "password";
    private final String loginButtonId = "loginButton";

    public void openLoginPage() {
        driver.get("http://your-website-url/login");
    }

    public void enterUsername(String username) {
        driver.findElement(By.id(usernameFieldId)).sendKeys(username);
    }

    public void enterPassword(String password) {
        driver.findElement(By.id(passwordFieldId)).sendKeys(password);
    }

    public void clickLoginButton() {
        driver.findElement(By.id(loginButtonId)).click();
    }
}