# Test Configuration Properties
# Application URLs
app.base.url=https://testing.roboost.app/#/login
app.login.url=/login
app.dashboard.url=/dashboard

# Browser Configuration
browser.default=chrome
browser.headless=false
browser.maximize=true

# Timeout Configuration (in seconds)
timeout.implicit=10
timeout.explicit=15
timeout.page.load=30
timeout.script=30

# Test Data Configuration
test.username=testuser
test.password=testpass123
test.invalid.username=invaliduser
test.invalid.password=invalidpass

# Screenshot Configuration
screenshot.on.failure=true
screenshot.directory=test-output/screenshots/

# Logging Configuration
log.level=INFO
log.file=test-output/logs/test.log

# Allure Reporting Configuration
allure.results.directory=target/allure-results
allure.report.directory=target/allure-report

# Retry Configuration
test.retry.count=1
test.retry.on.failure=true

# Environment Configuration
environment=test
test.environment.url=http://test-environment-url
staging.environment.url=http://staging-environment-url
production.environment.url=http://production-environment-url
