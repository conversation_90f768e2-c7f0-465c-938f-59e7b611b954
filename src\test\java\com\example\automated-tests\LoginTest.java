package com.example.automated-tests;

import com.example.automated-tests.webpages.LoginPage;
import org.testng.annotations.Test;
import static org.testng.Assert.assertTrue;

public class LoginTest extends LoginPage {

    @Test
    public void testSuccessfulLogin() {
        openLoginPage();
        enterUsername("yourUsername");
        enterPassword("yourPassword");
        clickLoginButton();

        // Add assertions to verify successful login (e.g., check if a specific element is present)
        String expectedTitle = "Dashboard";
        assertTrue(driver.getTitle().contains(expectedTitle),  "Test Failed: Dashboard title not found.");
    }

    @Test(expectedExceptions = AssertionError.class)
    public void testFailedLogin() {
        openLoginPage();
        enterUsername("wrongUsername");
        enterPassword("wrongPassword");
        clickLoginButton();

        // Normally, you wouldn't expect an assertion failure here but...
        // You can add more logic to handle such conditions.
    }
}