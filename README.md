# Login Page Test Automation Framework

A comprehensive Java-based test automation framework for testing login page functionality using Selenium WebDriver, TestNG, and modern testing practices.

## 🚀 Features

- **Modern Framework**: Built with Selenium 4.15+, TestNG 7.8+, and Java 11
- **Page Object Model**: Implements Page Factory pattern with proper encapsulation
- **Comprehensive Test Coverage**: 15+ test scenarios covering positive, negative, and edge cases
- **Automatic Driver Management**: Uses WebDriverManager for seamless browser driver handling
- **Rich Reporting**: Integrated with Allure for detailed test reports
- **Cross-Browser Support**: Chrome, Firefox, and Edge browser support
- **Fluent Assertions**: Uses AssertJ for readable and maintainable assertions
- **Screenshot Capture**: Automatic screenshot capture on test failures
- **Data-Driven Testing**: External test data management with TestNG DataProviders

## 📋 Test Coverage

### Positive Test Cases
- ✅ Valid login with correct credentials
- ✅ Multiple valid credential combinations
- ✅ Remember me functionality
- ✅ Login button functionality verification

### Negative Test Cases
- ❌ Invalid username/password combinations
- ❌ Empty username field validation
- ❌ Empty password field validation
- ❌ Empty both fields validation
- ❌ SQL injection attempt handling

### UI and Functional Tests
- 🔍 Page element visibility verification
- 🔍 Forgot password link functionality
- 🔍 Field clearing operations
- 🔍 Boundary condition testing
- 🔍 Special character handling
- 🔍 Page load performance testing

## 🛠️ Prerequisites

- **Java 11** or higher
- **Maven 3.6+**
- **Chrome/Firefox/Edge** browser installed
- **Git** for version control

## 📦 Dependencies

| Dependency | Version | Purpose |
|------------|---------|---------|
| Selenium WebDriver | 4.15.0 | Browser automation |
| TestNG | 7.8.0 | Testing framework |
| WebDriverManager | 5.6.2 | Automatic driver management |
| AssertJ | 3.24.2 | Fluent assertions |
| Allure TestNG | 2.24.0 | Test reporting |
| Apache Commons IO | 2.11.0 | File operations |
| SLF4J | 2.0.9 | Logging |

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd automated-tests
```

### 2. Install Dependencies
```bash
mvn clean install
```

### 3. Configure Test Environment
Update `src/test/resources/test.properties` with your application URLs:
```properties
app.base.url=http://your-application-url
test.username=your-test-username
test.password=your-test-password
```

### 4. Run Tests
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=LoginPageTest

# Run with specific browser
mvn test -Dbrowser=chrome

# Run with custom base URL
mvn test -DbaseUrl=http://your-custom-url
```

## 📊 Test Execution Options

### Browser Selection
```bash
mvn test -Dbrowser=chrome    # Default
mvn test -Dbrowser=firefox
mvn test -Dbrowser=edge
```

### Environment Configuration
```bash
mvn test -DbaseUrl=http://test-environment-url
mvn test -Denvironment=staging
```

### Parallel Execution
```bash
mvn test -DthreadCount=3 -Dparallel=methods
```

## 📈 Reporting

### Allure Reports
```bash
# Generate Allure report
mvn allure:report

# Serve Allure report
mvn allure:serve
```

### TestNG Reports
Reports are automatically generated in `target/surefire-reports/`

### Screenshots
Failed test screenshots are saved in `test-output/screenshots/`

## 🏗️ Project Structure

```
src/
├── main/java/com/example/automated-tests/
│   └── webpages/
│       ├── BasePage.java           # Legacy base page
│       └── LoginPage.java          # Enhanced login page object
└── test/java/com/example/automated-tests/
    ├── base/
    │   └── TestBase.java           # Modern test base class
    ├── data/
    │   └── TestDataProvider.java   # Test data management
    ├── tests/
    │   └── LoginPageTest.java      # Comprehensive test class
    └── utils/
        └── TestUtils.java          # Utility methods
```

## 🔧 Configuration Files

- `pom.xml` - Maven dependencies and build configuration
- `src/test/resources/testng.xml` - TestNG suite configuration
- `src/test/resources/test.properties` - Test environment properties

## 🎯 Best Practices Implemented

1. **Page Object Model**: Clean separation of page logic and test logic
2. **Page Factory Pattern**: Efficient element initialization
3. **Explicit Waits**: Reliable element interactions
4. **Fluent Assertions**: Readable test assertions
5. **Data-Driven Testing**: Externalized test data
6. **Proper Logging**: Comprehensive test execution logging
7. **Error Handling**: Graceful failure handling with screenshots
8. **Cross-Browser Testing**: Multi-browser support
9. **Parallel Execution**: Scalable test execution

## 🐛 Troubleshooting

### Common Issues

1. **WebDriver Issues**
   - Ensure browsers are installed and updated
   - WebDriverManager handles driver downloads automatically

2. **Test Failures**
   - Check application URL in test.properties
   - Verify test credentials are valid
   - Review screenshots in test-output/screenshots/

3. **Build Issues**
   - Ensure Java 11+ is installed
   - Run `mvn clean install` to refresh dependencies

## 🤝 Contributing

1. Follow existing code style and patterns
2. Add comprehensive test coverage for new features
3. Update documentation for any changes
4. Ensure all tests pass before submitting

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.
