package com.example.automated-tests.tests;

import com.example.automated-tests.base.TestBase;
import com.example.automated-tests.data.TestDataProvider;
import com.example.automated-tests.utils.TestUtils;
import com.example.automated-tests.webpages.LoginPage;
import io.qameta.allure.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testng.ITestResult;
import org.testng.annotations.*;

import static org.assertj.core.api.Assertions.*;

/**
 * Comprehensive test class for LoginPage functionality.
 * Tests various login scenarios including positive, negative, and edge cases.
 * Implements enterprise-level testing standards with proper reporting and error handling.
 * 
 * <AUTHOR> Automation Team
 * @version 1.0
 */
@Epic("Authentication")
@Feature("Login Functionality")
public class LoginPageTest extends TestBase {
    
    private static final Logger logger = LoggerFactory.getLogger(LoginPageTest.class);
    private LoginPage loginPage;
    
    /**
     * Sets up test-specific objects before each test method.
     */
    @BeforeMethod(dependsOnMethods = "setUp")
    public void setUpTest() {
        loginPage = new LoginPage(driver, wait);
        logger.info("LoginPage object initialized for test");
    }
    
    /**
     * Handles test failure scenarios by taking screenshots.
     * 
     * @param result TestNG ITestResult containing test execution details
     */
    @AfterMethod(alwaysRun = true)
    public void handleTestFailure(ITestResult result) {
        if (result.getStatus() == ITestResult.FAILURE) {
            String testName = result.getMethod().getMethodName();
            logger.error("Test failed: {}", testName);
            
            // Take screenshot for failed test
            TestUtils.takeScreenshot(driver, testName);
            TestUtils.takeScreenshotForAllure(driver);
        }
    }
    
    // Positive Test Cases
    
    @Test(priority = 1, description = "Verify successful login with valid credentials")
    @Story("Valid Login")
    @Severity(SeverityLevel.CRITICAL)
    @Description("Test successful login functionality with valid username and password")
    public void should_LoginSuccessfully_When_ValidCredentialsProvided() {
        // Arrange
        String username = TestDataProvider.LoginCredentials.VALID_USERNAME;
        String password = TestDataProvider.LoginCredentials.VALID_PASSWORD;
        
        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.performLogin(username, password);
        
        // Assert
        assertThat(loginPage.isLoginPageDisplayed())
            .as("Login page should not be displayed after successful login")
            .isFalse();
        
        // Verify redirect to dashboard (assuming successful login redirects)
        assertThat(driver.getCurrentUrl())
            .as("Should redirect to dashboard after successful login")
            .contains("/dashboard");
        
        logger.info("Valid login test completed successfully");
    }
    
    @Test(priority = 2, dataProvider = "validLoginData", dataProviderClass = TestDataProvider.class,
          description = "Verify login with multiple valid credential sets")
    @Story("Valid Login")
    @Severity(SeverityLevel.CRITICAL)
    public void should_LoginSuccessfully_When_MultipleValidCredentialsProvided(String username, String password) {
        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.performLogin(username, password);
        
        // Assert
        assertThat(driver.getCurrentUrl())
            .as("Should redirect after successful login with credentials: " + username)
            .doesNotContain("/login");
        
        logger.info("Multiple valid credentials test completed for user: {}", username);
    }
    
    @Test(priority = 3, description = "Verify remember me functionality")
    @Story("Remember Me")
    @Severity(SeverityLevel.NORMAL)
    public void should_RememberUser_When_RememberMeChecked() {
        // Arrange
        String username = TestDataProvider.LoginCredentials.VALID_USERNAME;
        String password = TestDataProvider.LoginCredentials.VALID_PASSWORD;
        
        // Act
        loginPage.openLoginPage(getBaseUrl());
        
        // Only test if remember me checkbox is present
        if (loginPage.isRememberMeCheckboxPresent()) {
            loginPage.performLoginWithRememberMe(username, password, true);
            
            // Assert
            assertThat(driver.getCurrentUrl())
                .as("Should redirect after successful login with remember me")
                .doesNotContain("/login");
        } else {
            logger.info("Remember me checkbox not present, skipping test");
        }
        
        logger.info("Remember me functionality test completed");
    }
    
    // Negative Test Cases
    
    @Test(priority = 4, dataProvider = "invalidLoginData", dataProviderClass = TestDataProvider.class,
          description = "Verify login failure with invalid credentials")
    @Story("Invalid Login")
    @Severity(SeverityLevel.CRITICAL)
    public void should_ShowErrorMessage_When_InvalidCredentialsProvided(String username, String password, String scenario) {
        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.performLogin(username, password);
        
        // Assert
        assertThat(loginPage.isErrorMessageDisplayed())
            .as("Error message should be displayed for scenario: " + scenario)
            .isTrue();
        
        assertThat(loginPage.getErrorMessageText())
            .as("Error message should not be empty for scenario: " + scenario)
            .isNotEmpty();
        
        assertThat(driver.getCurrentUrl())
            .as("Should remain on login page after failed login for scenario: " + scenario)
            .contains("/login");
        
        logger.info("Invalid credentials test completed for scenario: {}", scenario);
    }
    
    @Test(priority = 5, description = "Verify login with empty username")
    @Story("Field Validation")
    @Severity(SeverityLevel.HIGH)
    public void should_ShowValidationError_When_UsernameIsEmpty() {
        // Arrange
        String password = TestDataProvider.LoginCredentials.VALID_PASSWORD;
        
        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.enterPassword(password);
        loginPage.clickLoginButton();
        
        // Assert
        assertThat(loginPage.isErrorMessageDisplayed())
            .as("Error message should be displayed when username is empty")
            .isTrue();
        
        assertThat(driver.getCurrentUrl())
            .as("Should remain on login page when username is empty")
            .contains("/login");
        
        logger.info("Empty username validation test completed");
    }
    
    @Test(priority = 6, description = "Verify login with empty password")
    @Story("Field Validation")
    @Severity(SeverityLevel.HIGH)
    public void should_ShowValidationError_When_PasswordIsEmpty() {
        // Arrange
        String username = TestDataProvider.LoginCredentials.VALID_USERNAME;
        
        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.enterUsername(username);
        loginPage.clickLoginButton();
        
        // Assert
        assertThat(loginPage.isErrorMessageDisplayed())
            .as("Error message should be displayed when password is empty")
            .isTrue();
        
        assertThat(driver.getCurrentUrl())
            .as("Should remain on login page when password is empty")
            .contains("/login");
        
        logger.info("Empty password validation test completed");
    }
    
    @Test(priority = 7, description = "Verify login with both fields empty")
    @Story("Field Validation")
    @Severity(SeverityLevel.HIGH)
    public void should_ShowValidationError_When_BothFieldsAreEmpty() {
        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.clickLoginButton();
        
        // Assert
        assertThat(loginPage.isErrorMessageDisplayed())
            .as("Error message should be displayed when both fields are empty")
            .isTrue();
        
        assertThat(driver.getCurrentUrl())
            .as("Should remain on login page when both fields are empty")
            .contains("/login");
        
        logger.info("Empty fields validation test completed");
    }

    // Security Test Cases

    @Test(priority = 8, dataProvider = "sqlInjectionData", dataProviderClass = TestDataProvider.class,
          description = "Verify SQL injection protection")
    @Story("Security")
    @Severity(SeverityLevel.CRITICAL)
    public void should_PreventSQLInjection_When_MaliciousInputProvided(String username, String password) {
        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.performLogin(username, password);

        // Assert
        assertThat(loginPage.isErrorMessageDisplayed())
            .as("Error message should be displayed for SQL injection attempt")
            .isTrue();

        assertThat(driver.getCurrentUrl())
            .as("Should remain on login page after SQL injection attempt")
            .contains("/login");

        // Verify no successful login occurred
        assertThat(driver.getCurrentUrl())
            .as("Should not redirect to dashboard after SQL injection attempt")
            .doesNotContain("/dashboard");

        logger.info("SQL injection protection test completed for input: {}", username);
    }

    // UI Element Tests

    @Test(priority = 9, description = "Verify login page elements are displayed correctly")
    @Story("UI Elements")
    @Severity(SeverityLevel.NORMAL)
    public void should_DisplayAllElements_When_LoginPageLoaded() {
        // Act
        loginPage.openLoginPage(getBaseUrl());

        // Assert
        assertThat(loginPage.isLoginPageDisplayed())
            .as("Login page should be displayed correctly")
            .isTrue();

        assertThat(loginPage.isLoginPageTitleCorrect())
            .as("Login page title should be correct")
            .isTrue();

        assertThat(loginPage.isUsernameFieldEnabled())
            .as("Username field should be enabled")
            .isTrue();

        assertThat(loginPage.isPasswordFieldEnabled())
            .as("Password field should be enabled")
            .isTrue();

        assertThat(loginPage.isLoginButtonEnabled())
            .as("Login button should be enabled")
            .isTrue();

        logger.info("UI elements display test completed");
    }

    @Test(priority = 10, description = "Verify forgot password link functionality")
    @Story("Forgot Password")
    @Severity(SeverityLevel.NORMAL)
    public void should_NavigateToForgotPassword_When_ForgotPasswordLinkClicked() {
        // Act
        loginPage.openLoginPage(getBaseUrl());

        // Only test if forgot password link is present
        if (loginPage.isForgotPasswordLinkPresent()) {
            loginPage.clickForgotPasswordLink();

            // Assert
            assertThat(driver.getCurrentUrl())
                .as("Should navigate to forgot password page")
                .contains("forgot");
        } else {
            logger.info("Forgot password link not present, skipping test");
        }

        logger.info("Forgot password link test completed");
    }

    // Boundary and Edge Case Tests

    @Test(priority = 11, dataProvider = "boundaryTestData", dataProviderClass = TestDataProvider.class,
          description = "Verify boundary conditions for input fields")
    @Story("Boundary Testing")
    @Severity(SeverityLevel.NORMAL)
    public void should_HandleBoundaryConditions_When_BoundaryDataProvided(String username, String password, String scenario) {
        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.performLogin(username, password);

        // Assert - Should handle gracefully without crashes
        assertThat(driver.getCurrentUrl())
            .as("Application should handle boundary condition gracefully for scenario: " + scenario)
            .isNotEmpty();

        // Verify page is still functional
        assertThat(loginPage.isLoginPageDisplayed() || driver.getCurrentUrl().contains("/dashboard"))
            .as("Page should remain functional after boundary test for scenario: " + scenario)
            .isTrue();

        logger.info("Boundary test completed for scenario: {}", scenario);
    }

    @Test(priority = 12, dataProvider = "specialCharacterData", dataProviderClass = TestDataProvider.class,
          description = "Verify special character handling in input fields")
    @Story("Special Characters")
    @Severity(SeverityLevel.NORMAL)
    public void should_HandleSpecialCharacters_When_SpecialCharacterDataProvided(String username, String password, String scenario) {
        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.performLogin(username, password);

        // Assert
        assertThat(driver.getCurrentUrl())
            .as("Application should handle special characters gracefully for scenario: " + scenario)
            .isNotEmpty();

        // Verify input was accepted (fields should contain the entered values)
        assertThat(loginPage.getUsernameFieldValue())
            .as("Username field should accept special characters for scenario: " + scenario)
            .isNotEmpty();

        logger.info("Special character test completed for scenario: {}", scenario);
    }

    // Functional Tests

    @Test(priority = 13, description = "Verify field clearing functionality")
    @Story("Field Operations")
    @Severity(SeverityLevel.MINOR)
    public void should_ClearFields_When_ClearMethodCalled() {
        // Arrange
        String username = TestDataProvider.LoginCredentials.VALID_USERNAME;
        String password = TestDataProvider.LoginCredentials.VALID_PASSWORD;

        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.enterUsername(username);
        loginPage.enterPassword(password);

        // Verify fields have values
        assertThat(loginPage.getUsernameFieldValue())
            .as("Username field should contain entered value")
            .isEqualTo(username);

        // Clear fields
        loginPage.clearLoginFields();

        // Assert
        assertThat(loginPage.getUsernameFieldValue())
            .as("Username field should be empty after clearing")
            .isEmpty();

        assertThat(loginPage.getPasswordFieldValue())
            .as("Password field should be empty after clearing")
            .isEmpty();

        logger.info("Field clearing test completed");
    }

    @Test(priority = 14, description = "Verify login button functionality")
    @Story("Button Functionality")
    @Severity(SeverityLevel.NORMAL)
    public void should_SubmitForm_When_LoginButtonClicked() {
        // Arrange
        String username = TestDataProvider.LoginCredentials.VALID_USERNAME;
        String password = TestDataProvider.LoginCredentials.VALID_PASSWORD;

        // Act
        loginPage.openLoginPage(getBaseUrl());
        loginPage.enterUsername(username);
        loginPage.enterPassword(password);

        String urlBeforeClick = driver.getCurrentUrl();
        loginPage.clickLoginButton();

        // Assert
        // URL should change after clicking login button (either to dashboard or showing error)
        assertThat(driver.getCurrentUrl())
            .as("URL should change after clicking login button")
            .isNotEqualTo(urlBeforeClick);

        logger.info("Login button functionality test completed");
    }

    @Test(priority = 15, description = "Verify page load performance")
    @Story("Performance")
    @Severity(SeverityLevel.MINOR)
    public void should_LoadQuickly_When_LoginPageAccessed() {
        // Act & Assert
        long startTime = System.currentTimeMillis();
        loginPage.openLoginPage(getBaseUrl());
        long endTime = System.currentTimeMillis();

        long loadTime = endTime - startTime;

        assertThat(loadTime)
            .as("Login page should load within 5 seconds")
            .isLessThan(5000);

        assertThat(loginPage.isLoginPageDisplayed())
            .as("Login page should be fully loaded")
            .isTrue();

        logger.info("Page load performance test completed. Load time: {} ms", loadTime);
    }
}
